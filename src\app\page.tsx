import { Metadata } from 'next';
import Image from "next/image";
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import LocationOnIcon from '@mui/icons-material/LocationOn';

const mapPlaceholder = "https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=200&q=80";

export const metadata: Metadata = {
  title: 'Agricultural Dashboard',
  description: 'Agricultural monitoring and management dashboard',
};

export default function Home() {
  return (
    <main className="min-h-screen bg-gray-200 relative overflow-hidden font-sans">
      {/* Background */}
      <div className="absolute inset-0 -z-10 bg-gradient-to-br from-gray-100 to-gray-300" />

      <div className="max-w-7xl mx-auto p-6 grid gap-4">
        {/* Top Section */}
        <div className="grid grid-cols-12 gap-4">
          {/* Left Field Card */}
          <div className="col-span-2 bg-white rounded-2xl shadow-lg p-4 flex flex-col border border-gray-100">
            <div className="w-full h-24 mb-1 relative overflow-hidden rounded-xl">
              {/* Imagen aérea de stock */}
              <Image src="https://images.unsplash.com/photo-1501785888041-af3ef285b470?auto=format&fit=crop&w=400&q=80" alt="Corn field" fill className="object-cover" />
              {/* Polígono SVG */}
              <svg className="absolute inset-0 w-full h-full" viewBox="0 0 112 80" fill="none">
                <polygon points="22,58 38,28 85,22 95,48 68,68" fill="#D9F99D" fillOpacity="0.5" stroke="#A3E635" strokeWidth="2" />
              </svg>
            </div>
            <div className="flex items-center justify-between w-full mt-1 gap-2">
              <div className="flex flex-col">
                <div className="text-base font-bold leading-tight">Corn field</div>
                <div className="text-xs text-gray-400 leading-tight">18 ha</div>
              </div>
              <button className="bg-lime-300 hover:bg-lime-400 text-green-900 rounded-full w-8 h-8 flex items-center justify-center shadow transition-all duration-200">
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                  <path d="M8 12H16" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                  <path d="M13 9L16 12L13 15" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                </svg>
              </button>
            </div>
          </div>
          {/* Weather Card */}
          <div className="col-span-4 bg-white rounded-2xl shadow-lg p-4 flex flex-col justify-between border border-gray-100">
            <div className="flex items-center gap-1 text-gray-700 font-medium mb-2">
              <LocationOnIcon fontSize="small" className="text-gray-500" />
              <span className="text-xs">Vynnyky, Lviv Oblast, Ukraine</span>
            </div>
            <div className="flex items-center gap-2 mb-3">
              <div className="text-4xl font-extrabold text-gray-800">+16°</div>
              <div className="flex flex-col text-xs text-gray-500">
                <span>H: +19°</span>
                <span>L: +10°</span>
              </div>
              <div className="ml-auto">
                {/* Weather icon - cloudy */}
                <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                  <svg width="24" height="24" viewBox="0 0 32 32" fill="none">
                    <path d="M24 18C26.2091 18 28 16.2091 28 14C28 11.7909 26.2091 10 24 10C23.7 10 23.4 10.1 23.1 10.1C22.5 7.6 20.4 6 18 6C15.2 6 13 8.2 13 11C13 11.3 13 11.6 13.1 11.9C11.3 12.4 10 14 10 16C10 18.2 11.8 20 14 20H24Z" fill="#9CA3AF"/>
                  </svg>
                </div>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-1 text-xs text-gray-500">
              <div className="text-center">
                <div className="font-medium text-gray-700">Humidity</div>
                <div className="font-bold text-gray-800">74%</div>
              </div>
              <div className="text-center">
                <div className="font-medium text-gray-700">Precipitation</div>
                <div className="font-bold text-gray-800">5 mm</div>
              </div>
              <div className="text-center">
                <div className="font-medium text-gray-700">Pressure</div>
                <div className="font-bold text-gray-800">1019 hPa</div>
              </div>
              <div className="text-center">
                <div className="font-medium text-gray-700">Wind</div>
                <div className="font-bold text-gray-800">18 km/h</div>
              </div>
            </div>
          </div>
          {/* Rate Card */}
          <div className="col-span-4 bg-white rounded-2xl shadow-lg p-3 flex flex-col gap-2 border border-gray-100">
            <div className="flex flex-col gap-1 mb-2">
              <div className="flex items-center gap-1">
                <span className="text-xs text-gray-500">Standard rate</span>
                <input className="border rounded px-1 py-0.5 w-10 text-xs text-center" defaultValue="100" readOnly />
              </div>
              <div className="flex bg-gray-200 rounded-full p-0.5 gap-0.5">
                <button className="bg-lime-300 text-green-900 rounded-full px-1 py-0.5 text-xs font-semibold shadow">kg/ha</button>
                <button className="text-gray-500 rounded-full px-1 py-0.5 text-xs font-semibold">L/ha</button>
              </div>
            </div>
            <div className="flex justify-between items-center text-xs text-gray-500 mb-1">
              <span>Rate for zone</span>
              <button className="text-lime-600 hover:text-lime-700 font-medium text-xs">Edit</button>
            </div>
            <div className="grid grid-cols-4 gap-1">
              <div className="flex flex-col items-start bg-gray-50 rounded p-1">
                <div className="flex items-center gap-0.5 mb-0.5">
                  <span className="w-1 h-1 rounded-full bg-purple-500 inline-block"></span>
                  <span className="font-semibold text-gray-700 text-xs">Very high</span>
                </div>
                <div className="flex justify-between w-full text-xs">
                  <span className="font-bold text-gray-800">70</span>
                  <span className="text-gray-500">1,8 ha</span>
                </div>
              </div>
              <div className="flex flex-col items-start bg-gray-50 rounded p-1">
                <div className="flex items-center gap-0.5 mb-0.5">
                  <span className="w-1 h-1 rounded-full bg-green-500 inline-block"></span>
                  <span className="font-semibold text-gray-700 text-xs">High</span>
                </div>
                <div className="flex justify-between w-full text-xs">
                  <span className="font-bold text-gray-800">85</span>
                  <span className="text-gray-500">4,5 ha</span>
                </div>
              </div>
              <div className="flex flex-col items-start bg-gray-50 rounded p-1">
                <div className="flex items-center gap-0.5 mb-0.5">
                  <span className="w-1 h-1 rounded-full bg-blue-500 inline-block"></span>
                  <span className="font-semibold text-gray-700 text-xs">Average</span>
                </div>
                <div className="flex justify-between w-full text-xs">
                  <span className="font-bold text-gray-800">100</span>
                  <span className="text-gray-500">5,5 ha</span>
                </div>
              </div>
              <div className="flex flex-col items-start bg-gray-50 rounded p-1">
                <div className="flex items-center gap-0.5 mb-0.5">
                  <span className="w-1 h-1 rounded-full bg-gray-400 inline-block"></span>
                  <span className="font-semibold text-gray-700 text-xs">Low</span>
                </div>
                <div className="flex justify-between w-full text-xs">
                  <span className="font-bold text-gray-800">115</span>
                  <span className="text-gray-500">6,2 ha</span>
                </div>
              </div>
            </div>
          </div>
          {/* Right Field Card - Empty field */}
          <div className="col-span-2 bg-white rounded-2xl shadow-lg p-4 flex flex-col border border-gray-100">
            <div className="w-full h-24 mb-1 relative overflow-hidden rounded-xl">
              <Image src={mapPlaceholder} alt="Empty field" fill className="object-cover" />
              <svg className="absolute inset-0 w-full h-full" viewBox="0 0 112 80" fill="none">
                <polygon points="22,58 38,28 85,22 95,48 68,68" fill="#D9F99D" fillOpacity="0.5" stroke="#A3E635" strokeWidth="2" />
              </svg>
            </div>
            <div className="flex items-center justify-between w-full mt-1 gap-2">
              <div className="flex flex-col">
                <div className="text-base font-bold leading-tight">Empty field</div>
                <div className="text-xs text-gray-400 leading-tight">15 ha</div>
              </div>
              <button className="bg-lime-300 hover:bg-lime-400 text-green-900 rounded-full w-8 h-8 flex items-center justify-center shadow transition-all duration-200">
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                  <path d="M8 12H16" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                  <path d="M13 9L16 12L13 15" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                </svg>
              </button>
            </div>
          </div>
        </div>
        {/* Main Section */}
        <div className="grid grid-cols-5 gap-4 items-start">
          {/* Status Cards Block with background image and blur */}
          <div className="col-span-3 relative flex flex-col items-center min-h-[300px]">
            {/* Background image blurred */}
            <Image
              src="https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=800&q=80"
              alt="Campo"
              fill
              className="object-cover blur-md absolute inset-0 z-0 rounded-3xl"
              style={{objectPosition: 'center'}}
            />
            {/* Dark translucent overlay */}
            <div className="absolute inset-0 bg-black bg-opacity-50 z-10 rounded-3xl" />
            {/* Cards */}
            <div className="relative z-20 flex w-full gap-4 p-6">
              {/* Plant's health Card */}
              <div className="flex-1 rounded-2xl bg-black/50 backdrop-blur-md shadow-xl p-6 flex flex-col items-center">
                <span className="mb-1 block">
                  {/* Icono Plant's health (contorno de hojas, verde) */}
                  <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g filter="url(#ph-blur)">
                      <ellipse cx="18" cy="28" rx="8" ry="3" fill="#22C55E" fillOpacity="0.15"/>
                    </g>
                    <path d="M18 28C18 28 10 22 10 15C10 10 14 6 18 6C22 6 26 10 26 15C26 22 18 28 18 28Z" stroke="#22C55E" strokeWidth="2" fill="none"/>
                    <path d="M18 28C18 28 14 22 14 17C14 14 16 12 18 12C20 12 22 14 22 17C22 22 18 28 18 28Z" stroke="#22C55E" strokeWidth="2" fill="none"/>
                    <defs>
                      <filter id="ph-blur" x="6" y="23" width="24" height="10" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
                        <feGaussianBlur stdDeviation="1.5"/>
                      </filter>
                    </defs>
                  </svg>
                </span>
                <span className="text-4xl font-extrabold text-white drop-shadow">93%</span>
                <div className="text-white text-xs mb-1 drop-shadow">of reference value</div>
                <div className="text-white font-semibold text-lg drop-shadow">Plant's health</div>
              </div>
              {/* Water depth Card */}
              <div className="flex-1 rounded-2xl bg-black/50 backdrop-blur-md shadow-xl p-6 flex flex-col items-center">
                <span className="mb-1 block">
                  {/* Icono Water depth (gota de agua, azul) */}
                  <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g filter="url(#wd-blur)">
                      <ellipse cx="18" cy="28" rx="8" ry="3" fill="#38BDF8" fillOpacity="0.15"/>
                    </g>
                    <path d="M18 7C18 7 26 17 26 23C26 27 22.4183 31 18 31C13.5817 31 10 27 10 23C10 17 18 7 18 7Z" stroke="#BAE6FD" strokeWidth="2" fill="#38BDF8"/>
                    <defs>
                      <filter id="wd-blur" x="6" y="23" width="24" height="10" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
                        <feGaussianBlur stdDeviation="1.5"/>
                      </filter>
                    </defs>
                  </svg>
                </span>
                <span className="text-4xl font-extrabold text-white drop-shadow">85%</span>
                <div className="text-white text-xs mb-1 drop-shadow">of reference value</div>
                <div className="text-white font-semibold text-lg drop-shadow">Water depth</div>
              </div>
              {/* Soil Card */}
              <div className="flex-1 rounded-2xl bg-black/50 backdrop-blur-md shadow-xl p-6 flex flex-col items-center">
                <span className="mb-1 block">
                  {/* Icono Soil (globo terráqueo, amarillo/verde) */}
                  <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g filter="url(#soil-blur)">
                      <ellipse cx="18" cy="28" rx="8" ry="3" fill="#FACC15" fillOpacity="0.15"/>
                    </g>
                    <circle cx="18" cy="17" r="7" stroke="#FACC15" strokeWidth="2" fill="#FDE68A"/>
                    <path d="M11 17C11 17 13.5 14 18 14C22.5 14 25 17 25 17" stroke="#22C55E" strokeWidth="1.5" fill="none"/>
                    <path d="M18 10C18 10 18 24 18 24" stroke="#22C55E" strokeWidth="1.5" fill="none"/>
                    <defs>
                      <filter id="soil-blur" x="6" y="23" width="24" height="10" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
                        <feGaussianBlur stdDeviation="1.5"/>
                      </filter>
                    </defs>
                  </svg>
                </span>
                <span className="text-4xl font-extrabold text-white drop-shadow">74%</span>
                <div className="text-white text-xs mb-1 drop-shadow">of reference value</div>
                <div className="text-white font-semibold text-lg drop-shadow">Soil</div>
              </div>
            </div>
            <div className="relative z-20 flex justify-between w-full text-white text-base drop-shadow mt-4 px-6">
              <span>10 days to harvest</span>
              <span>64/74</span>
            </div>
            {/* Progress bar */}
            <div className="relative z-20 w-full flex items-center justify-center px-6 mt-2">
              <div className="w-full h-2 bg-white/20 rounded-full overflow-hidden shadow-inner">
                <div
                  className="h-full bg-lime-400 rounded-full transition-all duration-500"
                  style={{ width: `${(64/74)*100}%` }}
                />
              </div>
            </div>
            {/* Extra space */}
            <div className="h-6" />
          </div>

          {/* Try for Free Vertical Button */}
          <div className="col-span-1 flex flex-col items-center justify-center h-full">
            <div className="bg-lime-400 hover:bg-lime-500 text-green-900 font-bold rounded-full shadow-2xl transition-all duration-200 flex items-center justify-center h-full min-h-[300px] w-16 relative">
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="transform -rotate-90 flex items-center gap-2">
                  <span className="text-sm font-bold tracking-wider">TRY FOR FREE</span>
                  <div className="w-8 h-8 bg-green-700 rounded-full flex items-center justify-center">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                      <path d="M8 3L8 13" stroke="white" strokeWidth="2" strokeLinecap="round"/>
                      <path d="M3 8L8 3L13 8" stroke="white" strokeWidth="2" strokeLinecap="round"/>
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Map Card */}
          <div className="col-span-1 bg-white rounded-2xl shadow-lg p-4 flex flex-col gap-3">
            <div className="relative w-full h-32 mb-2">
              <Image src={mapPlaceholder} alt="Map" fill className="rounded-lg object-cover" />
              {/* Overlay with field data */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="bg-green-600 text-white text-xs px-2 py-1 rounded font-bold shadow text-center">
                  High<br/>85 kg/h<br/>4.5 ha
                </div>
              </div>
              {/* Purple marker */}
              <div className="absolute top-2 right-2 w-3 h-3 bg-purple-500 rounded-full border-2 border-white"></div>
            </div>
            <div className="space-y-1">
              <div className="text-xs text-gray-500">
                Low <span className="bg-gray-200 text-gray-700 rounded px-1 font-bold text-xs">115 kg/h</span> <span className="text-gray-400">6.2 ha</span>
              </div>
              <div className="text-xs text-gray-500">Phosphorus <b className="text-gray-700">10.5</b></div>
              <div className="text-xs text-gray-500">Magnesium <b className="text-gray-700">7.2</b></div>
              <div className="text-xs text-gray-500">Acidity <b className="text-gray-700">3.0</b></div>
              <div className="text-xs text-gray-500">Humidity <b className="text-gray-700">38%</b></div>
            </div>
            <button className="bg-white border border-gray-300 text-gray-700 rounded-full w-8 h-8 flex items-center justify-center self-end mt-2 hover:bg-gray-50 transition-colors">
              <ArrowForwardIcon fontSize="small" />
            </button>
          </div>
        </div>
        {/* Bottom Section */}
        <div className="grid grid-cols-5 gap-4 items-center">
          {/* Crop Selector and Standard Rate */}
          <div className="col-span-3 bg-white rounded-2xl shadow-lg p-4 flex items-center gap-4 border border-gray-100">
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-500">Crop</span>
              <select className="border rounded px-3 py-2 text-sm bg-lime-100 text-green-800 font-medium">
                <option>Corn, grain</option>
                <option>Wheat</option>
                <option>Barley</option>
              </select>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-500">Standard rate</span>
              <input className="border rounded px-3 py-2 w-16 text-sm text-center" defaultValue="100" readOnly />
              <span className="text-sm bg-lime-300 text-green-800 rounded px-2 py-1 font-semibold">kg/ha</span>
              <span className="text-sm bg-gray-200 text-gray-600 rounded px-2 py-1">L/ha</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-500">Productivity zones</span>
              <div className="flex gap-1">
                {[3,4,5,6,7].map((zone) => (
                  <span key={zone} className={`rounded px-2 py-1 text-sm font-bold ${zone === 4 ? 'bg-lime-300 text-green-900' : 'bg-gray-200 text-gray-600'}`}>
                    {zone}
                  </span>
                ))}
              </div>
            </div>
          </div>

          {/* Growth Rate Chart */}
          <div className="col-span-2 bg-white rounded-2xl shadow-lg p-4 flex flex-col border border-gray-100">
            <div className="flex items-center justify-between mb-3">
              <span className="text-sm text-gray-500">Growth rate</span>
              <div className="flex items-center gap-1 text-xs text-gray-400">
                <span>W</span>
                <span>M</span>
                <span>Y</span>
              </div>
            </div>
            <div className="flex items-center gap-2 mb-3">
              <span className="text-2xl font-extrabold">0.75</span>
              <span className="text-green-500 text-lg">↓</span>
            </div>
            {/* Chart */}
            <div className="w-full h-16 bg-gray-50 rounded flex items-end gap-1 px-2 py-2">
              {/* Simulated bar chart with lime colors */}
              {[4,8,6,10,7,12,8,6,9,5,7,8,10,6,8,7].map((v,i) => (
                <div key={i} className="bg-lime-400 rounded-sm flex-1" style={{height: `${v*4}px`}}></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
